{"name": "kubecloud", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --ext .js,.ts,.vue", "format": "prettier --write src/", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "cypress run", "test:e2e:dev": "cypress open"}, "dependencies": {"@mdi/font": "^7.4.47", "@stripe/stripe-js": "^7.4.0", "jsrsasign": "^11.1.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.4.1", "three": "^0.177.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuetify": "^3.8.10"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/jsrsasign": "^10.5.15", "@types/node": "^22.14.0", "@types/three": "^0.177.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/ui": "^1.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.7.0", "cypress": "^13.0.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "happy-dom": "^13.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vuetify": "^2.1.1", "vitest": "^1.0.0", "vue-tsc": "^2.2.8"}}
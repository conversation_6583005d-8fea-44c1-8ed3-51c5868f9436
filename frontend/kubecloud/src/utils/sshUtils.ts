/**
 * SSH key generation utilities for converting Web Crypto API keys to SSH format
 */

import jsrsasign from 'jsrsasign'

export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

/**
 * Convert ECDSA P-256 public key to SSH format
 */
export async function generateSSHECDSAKey(keyPair: CryptoKeyPair): Promise<string> {
  const spki = await crypto.subtle.exportKey('spki', keyPair.publicKey)
  const pem = arrayBufferToPem(spki, 'PUBLIC KEY')

  try {
    // Parse the PEM using jsrsasign
    const publicKey = jsrsasign.KEYUTIL.getKey(pem) as any

    if (!publicKey || typeof publicKey !== 'object' || !('x' in publicKey) || !('y' in publicKey)) {
      throw new Error('Invalid ECDSA public key')
    }

    // Extract x and y coordinates for P-256 curve
    const x = hexToBytes(publicKey.x as string)
    const y = hexToBytes(publicKey.y as string)

    // Create SSH wire format for ECDSA-SHA2-NISTP256
    const keyType = 'ecdsa-sha2-nistp256'
    const curveName = 'nistp256'

    // Build the SSH key data
    const keyData = buildSSHKeyData([
      stringToBytes(keyType),
      stringToBytes(curveName),
      new Uint8Array([0x04, ...x, ...y]) // Uncompressed point format
    ])

    const base64Key = arrayBufferToBase64(keyData.buffer as ArrayBuffer)
    return `${keyType} ${base64Key}`
  } catch (error) {
    console.error('Error converting ECDSA key to SSH format:', error)
    throw new Error('Failed to convert ECDSA key to SSH format')
  }
}

/**
 * Convert RSA public key to SSH format
 */
export async function generateSSHRSAKey(keyPair: CryptoKeyPair): Promise<string> {
  const spki = await crypto.subtle.exportKey('spki', keyPair.publicKey)
  const pem = arrayBufferToPem(spki, 'PUBLIC KEY')

  try {
    // Parse the PEM using jsrsasign
    const publicKey = jsrsasign.KEYUTIL.getKey(pem) as any

    if (!publicKey || typeof publicKey !== 'object' || !('n' in publicKey) || !('e' in publicKey)) {
      throw new Error('Invalid RSA public key')
    }

    // Extract modulus and exponent
    const n = hexToBytes(publicKey.n as string)
    const e = hexToBytes(publicKey.e as string)

    // Create SSH wire format for RSA
    const keyType = 'ssh-rsa'

    // Build the SSH key data
    const keyData = buildSSHKeyData([
      stringToBytes(keyType),
      e,
      n
    ])

    const base64Key = arrayBufferToBase64(keyData.buffer as ArrayBuffer)
    return `${keyType} ${base64Key}`
  } catch (error) {
    console.error('Error converting RSA key to SSH format:', error)
    throw new Error('Failed to convert RSA key to SSH format')
  }
}

export function arrayBufferToPem(buffer: ArrayBuffer, label: string): string {
  const base64 = arrayBufferToBase64(buffer)
  const lines = base64.match(/.{1,64}/g) || []
  return `-----BEGIN ${label}-----\n${lines.join('\n')}\n-----END ${label}-----\n`
}

/**
 * Helper functions for SSH key format conversion
 */

function hexToBytes(hex: string): Uint8Array {
  // Remove any leading zeros and ensure even length
  hex = hex.replace(/^0+/, '') || '0'
  if (hex.length % 2 !== 0) {
    hex = '0' + hex
  }

  const bytes = new Uint8Array(hex.length / 2)
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substr(i, 2), 16)
  }
  return bytes
}

function stringToBytes(str: string): Uint8Array {
  return new TextEncoder().encode(str)
}

function buildSSHKeyData(components: Uint8Array[]): Uint8Array {
  // Calculate total length
  let totalLength = 0
  for (const component of components) {
    totalLength += 4 + component.length // 4 bytes for length + data
  }

  const result = new Uint8Array(totalLength)
  let offset = 0

  for (const component of components) {
    // Write length (big-endian 32-bit)
    const length = component.length
    result[offset] = (length >>> 24) & 0xff
    result[offset + 1] = (length >>> 16) & 0xff
    result[offset + 2] = (length >>> 8) & 0xff
    result[offset + 3] = length & 0xff
    offset += 4

    // Write data
    result.set(component, offset)
    offset += component.length
  }

  return result
}

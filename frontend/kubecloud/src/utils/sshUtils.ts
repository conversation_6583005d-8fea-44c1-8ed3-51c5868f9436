/**
 * SSH key generation utilities - simplified and reliable approach
 */

/**
 * Generate an SSH key pair using the most reliable method
 * Tries Ed25519 first, falls back to RSA if not supported
 */
export async function generateSSHKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
  try {
    // Try Ed25519 first (modern, secure, and simple)
    return await generateEd25519KeyPair()
  } catch (error) {
    console.warn('Ed25519 not supported, falling back to RSA:', error)
    // Fallback to RSA if Ed25519 is not supported
    return await generateRSAKeyPair()
  }
}

/**
 * Generate an Ed25519 SSH key pair
 */
async function generateEd25519KeyPair(): Promise<{ publicKey: string; privateKey: string }> {
  // Generate Ed25519 key pair using Web Crypto API
  const keyPair = await crypto.subtle.generateKey(
    {
      name: 'Ed25519',
    },
    true, // extractable
    ['sign', 'verify']
  )

  // Export the public key in raw format
  const publicKeyRaw = await crypto.subtle.exportKey('raw', keyPair.publicKey)

  // Export the private key in PKCS8 format
  const privateKeyPkcs8 = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey)

  // Convert public key to SSH format
  const publicKeySSH = await convertEd25519ToSSH(publicKeyRaw)

  // Convert private key to PEM format
  const privateKeyPEM = arrayBufferToPem(privateKeyPkcs8, 'PRIVATE KEY')

  return {
    publicKey: publicKeySSH,
    privateKey: privateKeyPEM
  }
}

/**
 * Generate an RSA SSH key pair (fallback)
 */
async function generateRSAKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
  // Generate RSA key pair using Web Crypto API
  const keyPair = await crypto.subtle.generateKey(
    {
      name: 'RSASSA-PKCS1-v1_5',
      modulusLength: 2048,
      publicExponent: new Uint8Array([1, 0, 1]), // 65537
      hash: 'SHA-256',
    },
    true, // extractable
    ['sign', 'verify']
  )

  // Export the public key in SPKI format
  const publicKeySpki = await crypto.subtle.exportKey('spki', keyPair.publicKey)

  // Export the private key in PKCS8 format
  const privateKeyPkcs8 = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey)

  // Convert public key to SSH format
  const publicKeySSH = await convertRSAToSSH(publicKeySpki)

  // Convert private key to PEM format
  const privateKeyPEM = arrayBufferToPem(privateKeyPkcs8, 'PRIVATE KEY')

  return {
    publicKey: publicKeySSH,
    privateKey: privateKeyPEM
  }
}

/**
 * Convert Ed25519 raw public key to SSH format
 */
async function convertEd25519ToSSH(publicKeyRaw: ArrayBuffer): Promise<string> {
  const keyType = 'ssh-ed25519'
  const publicKeyBytes = new Uint8Array(publicKeyRaw)

  // Build SSH wire format for Ed25519
  const keyTypeBytes = new TextEncoder().encode(keyType)

  // Calculate total length needed
  const totalLength = 4 + keyTypeBytes.length + 4 + publicKeyBytes.length
  const sshKeyData = new Uint8Array(totalLength)

  let offset = 0

  // Write key type length and data
  writeUint32BE(sshKeyData, keyTypeBytes.length, offset)
  offset += 4
  sshKeyData.set(keyTypeBytes, offset)
  offset += keyTypeBytes.length

  // Write public key length and data
  writeUint32BE(sshKeyData, publicKeyBytes.length, offset)
  offset += 4
  sshKeyData.set(publicKeyBytes, offset)

  // Convert to base64
  const base64Key = arrayBufferToBase64(sshKeyData.buffer)

  return `${keyType} ${base64Key}`
}

/**
 * Convert RSA SPKI public key to SSH format
 */
async function convertRSAToSSH(publicKeySpki: ArrayBuffer): Promise<string> {
  const keyType = 'ssh-rsa'

  // Parse the SPKI to extract RSA parameters
  const spkiBytes = new Uint8Array(publicKeySpki)

  // For simplicity, we'll use a basic approach to extract the RSA public key
  // This extracts the modulus and exponent from the SPKI structure
  const { modulus, exponent } = parseRSAPublicKey(spkiBytes)

  // Build SSH wire format for RSA
  const keyTypeBytes = new TextEncoder().encode(keyType)

  // Calculate total length needed
  const totalLength = 4 + keyTypeBytes.length + 4 + exponent.length + 4 + modulus.length
  const sshKeyData = new Uint8Array(totalLength)

  let offset = 0

  // Write key type length and data
  writeUint32BE(sshKeyData, keyTypeBytes.length, offset)
  offset += 4
  sshKeyData.set(keyTypeBytes, offset)
  offset += keyTypeBytes.length

  // Write exponent length and data
  writeUint32BE(sshKeyData, exponent.length, offset)
  offset += 4
  sshKeyData.set(exponent, offset)
  offset += exponent.length

  // Write modulus length and data
  writeUint32BE(sshKeyData, modulus.length, offset)
  offset += 4
  sshKeyData.set(modulus, offset)

  // Convert to base64
  const base64Key = arrayBufferToBase64(sshKeyData.buffer)

  return `${keyType} ${base64Key}`
}

/**
 * Parse RSA public key from SPKI format to extract modulus and exponent
 */
function parseRSAPublicKey(spkiBytes: Uint8Array): { modulus: Uint8Array; exponent: Uint8Array } {
  // This is a simplified parser for RSA SPKI format
  // In a real implementation, you'd want a proper ASN.1 parser

  // Standard RSA exponent (65537)
  const exponent = new Uint8Array([0x01, 0x00, 0x01])

  // For a 2048-bit key, find the modulus in the SPKI structure
  // This is a simplified approach - look for the large integer that represents the modulus
  let modulusStart = -1
  let modulusLength = 0

  // Look for the modulus (it's the largest integer in the structure)
  for (let i = 0; i < spkiBytes.length - 256; i++) {
    // Look for ASN.1 INTEGER tag (0x02) followed by length indicating ~256 bytes
    if (spkiBytes[i] === 0x02 && spkiBytes[i + 1] === 0x82 && spkiBytes[i + 2] === 0x01) {
      modulusLength = (spkiBytes[i + 3] << 8) | spkiBytes[i + 4]
      modulusStart = i + 5
      break
    }
  }

  if (modulusStart === -1) {
    throw new Error('Could not parse RSA modulus from SPKI')
  }

  const modulus = spkiBytes.slice(modulusStart, modulusStart + modulusLength)

  return { modulus, exponent }
}

/**
 * Write a 32-bit big-endian unsigned integer to a Uint8Array
 */
function writeUint32BE(buffer: Uint8Array, value: number, offset: number): void {
  buffer[offset] = (value >>> 24) & 0xff
  buffer[offset + 1] = (value >>> 16) & 0xff
  buffer[offset + 2] = (value >>> 8) & 0xff
  buffer[offset + 3] = value & 0xff
}

/**
 * Convert ArrayBuffer to base64 string
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

/**
 * Convert ArrayBuffer to PEM format
 */
export function arrayBufferToPem(buffer: ArrayBuffer, label: string): string {
  const base64 = arrayBufferToBase64(buffer)
  const lines = base64.match(/.{1,64}/g) || []
  return `-----BEGIN ${label}-----\n${lines.join('\n')}\n-----END ${label}-----\n`
}
